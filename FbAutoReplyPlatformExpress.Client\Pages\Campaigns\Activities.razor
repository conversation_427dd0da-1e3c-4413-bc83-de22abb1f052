@page "/campaigns/{CampaignId:guid}/activities"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Permissions
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.Application.Dtos
@using System.Linq
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IAutoReplyCampaignService CampaignService
@inject NavigationManager NavigationManager
@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.ViewActivities)]

<PageTitle>Campaign Activities</PageTitle>

<Card>
    <CardHeader>
        <Row Class="justify-content-between">
            <Column ColumnSize="ColumnSize.IsAuto">
                <h2>
                    <Icon Name="IconName.List" />
                    Campaign Activities
                </h2>
                @if (Campaign != null)
                {
                    <p class="mb-0 text-muted">@Campaign.CampaignName</p>
                }
            </Column>
            <Column ColumnSize="ColumnSize.IsAuto">
                <Button Color="Color.Secondary" Clicked="NavigateBack">
                    <Icon Name="IconName.ArrowLeft" />
                    Back to Campaigns
                </Button>
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        @if (IsLoading && Campaign == null)
        {
            @* This spinner is for the initial load of the Campaign details *@
            <div class="text-center p-4">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading campaign details...</p>
            </div>
        }
        else if (Campaign != null)
        {
            <!-- Campaign Summary -->
            <Card Class="mb-4">
                <CardHeader>
                    <h5>Campaign Summary</h5>
                </CardHeader>
                <CardBody>
                    <Row>
                        <Column ColumnSize="ColumnSize.Is3">
                            <div class="text-center">
                                <h4 class="text-primary">@Campaign.TotalRepliesSent</h4>
                                <small class="text-muted">Total Replies</small>
                            </div>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is3">
                            <div class="text-center">
                                <h4 class="text-info">@Campaign.PublicRepliesSent</h4>
                                <small class="text-muted">Public Replies</small>
                            </div>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is3">
                            <div class="text-center">
                                <h4 class="text-warning">@Campaign.PrivateRepliesSent</h4>
                                <small class="text-muted">Private Messages</small>
                            </div>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is3">
                            <div class="text-center">
                                @if (Campaign.IsActive)
                                {
                                    <Badge Color="Color.Success" Class="fs-6">Active</Badge>
                                }
                                else
                                {
                                    <Badge Color="Color.Secondary" Class="fs-6">Inactive</Badge>
                                }
                                <br /><small class="text-muted">Status</small>
                            </div>
                        </Column>
                    </Row>
                </CardBody>
            </Card>


            <!-- Filters -->
            <Card Class="mb-3">
                <CardBody>
                    <Row>
                        <Column ColumnSize="ColumnSize.Is2">
                            <Field>
                                <FieldLabel>Status</FieldLabel>
                                <Select TValue="bool?" @bind-SelectedValue="HasErrorFilter" @onchange="OnErrorFilterChanged">
                                    <SelectItem TValue="bool?" Value="null">All</SelectItem>
                                    <SelectItem TValue="bool?" Value="false">Success</SelectItem>
                                    <SelectItem TValue="bool?" Value="true">Errors</SelectItem>
                                </Select>
                            </Field>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is2">
                            <Field>
                                <FieldLabel>Public Reply</FieldLabel>
                                <Select TValue="bool?" @bind-SelectedValue="PublicReplySentFilter" @onchange="OnPublicReplyFilterChanged">
                                    <SelectItem TValue="bool?" Value="null">All</SelectItem>
                                    <SelectItem TValue="bool?" Value="true">Sent</SelectItem>
                                    <SelectItem TValue="bool?" Value="false">Not Sent</SelectItem>
                                </Select>
                            </Field>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is2">
                            <Field>
                                <FieldLabel>Private Message</FieldLabel>
                                <Select TValue="bool?" @bind-SelectedValue="PrivateReplySentFilter" @onchange="OnPrivateReplyFilterChanged">
                                    <SelectItem TValue="bool?" Value="null">All</SelectItem>
                                    <SelectItem TValue="bool?" Value="true">Sent</SelectItem>
                                    <SelectItem TValue="bool?" Value="false">Not Sent</SelectItem>
                                </Select>
                            </Field>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is4">
                            <Field>
                                <FieldLabel>Search</FieldLabel>
                                <TextEdit @bind-Text="SearchText" Placeholder="Search by commenter name or comment..." @onkeypress="OnSearchKeyPress" />
                            </Field>
                        </Column>
                        <Column ColumnSize="ColumnSize.Is2">
                            <Field>
                                <FieldLabel>&nbsp;</FieldLabel>
                                <Button Color="Color.Primary" Clicked="SearchActivitiesAsync" Block="true">
                                    <Icon Name="IconName.Search" />
                                    Search
                                </Button>
                            </Field>
                        </Column>
                    </Row>
                </CardBody>
            </Card>

            <DataGrid @ref="activitiesDataGrid"
                      TItem="CampaignActivityDto"
                      Data="CampaignActivities?.Items"
                      ReadData="OnDataGridReadAsync"
                      TotalItems="(int)(CampaignActivities?.TotalCount ?? 0)"
                      ShowPager="true"
                      PageSize="PageSize"
                      Responsive="true">
                <DataGridColumns>
                    <DataGridColumn TItem="CampaignActivityDto" Field="@nameof(CampaignActivityDto.CommenterName)" Caption="Commenter" Sortable="true" Width="150px">
                        <DisplayTemplate>
                            <strong>@context.CommenterName</strong>
                            <br />
                            <small class="text-muted">@context.CommentCreatedAt.ToString("MMM dd, HH:mm")</small>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="CampaignActivityDto" Field="@nameof(CampaignActivityDto.OriginalComment)" Caption="Original Comment" Sortable="false">
                        <DisplayTemplate>
                            <div style="max-width: 250px;">
                                @(context.OriginalComment.Length > 100 ? context.OriginalComment.Substring(0, 100) + "..." : context.OriginalComment)
                            </div>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="CampaignActivityDto" Field="@nameof(CampaignActivityDto.PublicReplySent)" Caption="Public Reply" Sortable="true" Width="120px">
                        <DisplayTemplate>
                            @if (context.PublicReplySent)
                            {
                                <Badge Color="Color.Success">
                                    <Icon Name="IconName.CheckCircle" />
                                    Sent
                                </Badge>
                                @if (context.PublicReplyAt.HasValue)
                                {
                                    <br />

                                    <small class="text-muted">@context.PublicReplyAt.Value.ToString("HH:mm")</small>
                                }
                            }
                            else
                            {
                                <Badge Color="Color.Secondary">Not Sent</Badge>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="CampaignActivityDto" Field="@nameof(CampaignActivityDto.PrivateReplySent)" Caption="Private Message" Sortable="true" Width="120px">
                        <DisplayTemplate>
                            @if (context.PrivateReplySent)
                            {
                                <Badge Color="Color.Success">
                                    <Icon Name="IconName.CheckCircle" />
                                    Sent
                                </Badge>
                                @if (context.PrivateReplyAt.HasValue)
                                {
                                    <br />

                                    <small class="text-muted">@context.PrivateReplyAt.Value.ToString("HH:mm")</small>
                                }
                            }
                            else
                            {
                                <Badge Color="Color.Secondary">Not Sent</Badge>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="CampaignActivityDto" Field="@nameof(CampaignActivityDto.HasError)" Caption="Status" Sortable="true" Width="100px">
                        <DisplayTemplate>
                            @if (context.HasError)
                            {
                                <Badge Color="Color.Danger">
                                    <Icon Name="IconName.ExclamationTriangle" />
                                    Error
                                </Badge>
                                @if (context.RetryCount > 0)
                                {
                                    <br />

                                    <small class="text-muted">Retries: @context.RetryCount</small>
                                }
                            }
                            else
                            {
                                <Badge Color="Color.Success">
                                    <Icon Name="IconName.CheckCircle" />
                                    Success
                                </Badge>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="CampaignActivityDto" Caption="Actions" Sortable="false" Width="100px">
                        <DisplayTemplate>
                            <Button Color="Color.Info" Size="Size.Small" Clicked="() => ViewActivityDetailsAsync(context)">
                                <Icon Name="IconName.Eye" />
                                Details
                            </Button>
                        </DisplayTemplate>
                    </DataGridColumn>
                </DataGridColumns>
                <LoadingTemplate>
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading activities...</p>
                    </div>
                </LoadingTemplate>
                <EmptyTemplate>
                    <Alert Color="Color.Info" Visible="true" Class="mt-3">
                        <Icon Name="IconName.Info" />
                        No activities found for this campaign.
                    </Alert>
                </EmptyTemplate>
            </DataGrid>
        }
        else if (!IsLoading)
        {
            <Alert Color="Color.Danger" Visible="true">
                <Icon Name="IconName.ExclamationTriangle" />
                Could not load the specified campaign. It may have been deleted.
            </Alert>
        }
    </CardBody>
</Card>

<!-- Activity Details Modal -->
<Modal @ref="ActivityDetailsModal">
    <ModalContent Centered="true" Size="ModalSize.Large">
        <ModalHeader>
            <ModalTitle>Activity Details</ModalTitle>
            <CloseButton />
        </ModalHeader>
        <ModalBody>
            @if (SelectedActivity != null)
            {
                <Row>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Card>
                            <CardHeader>
                                <h6>Comment Information</h6>
                            </CardHeader>
                            <CardBody>
                                <p><strong>Commenter:</strong> @SelectedActivity.CommenterName</p>
                                <p><strong>Comment Time:</strong> @SelectedActivity.CommentCreatedAt.ToString("MMM dd, yyyy HH:mm:ss")</p>
                                <p><strong>Original Comment:</strong></p>
                                <div class="border p-2 bg-light">
                                    @SelectedActivity.OriginalComment
                                </div>
                            </CardBody>
                        </Card>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is6">
                        <Card>
                            <CardHeader>
                                <h6>Reply Information</h6>
                            </CardHeader>
                            <CardBody>
                                @if (SelectedActivity.PublicReplySent)
                                {
                                    <div class="mb-3">
                                        <strong>Public Reply:</strong>
                                        <div class="border p-2 bg-light mt-1">
                                            @SelectedActivity.PublicReplyMessage
                                        </div>
                                        <small class="text-muted">Sent at: @SelectedActivity.PublicReplyAt?.ToString("MMM dd, yyyy HH:mm:ss")</small>
                                    </div>
                                }

                                @if (SelectedActivity.PrivateReplySent)
                                {
                                    <div class="mb-3">
                                        <strong>Private Message:</strong>
                                        <div class="border p-2 bg-light mt-1">
                                            @SelectedActivity.PrivateReplyMessage
                                        </div>
                                        <small class="text-muted">Sent at: @SelectedActivity.PrivateReplyAt?.ToString("MMM dd, yyyy HH:mm:ss")</small>
                                    </div>
                                }

                                @if (SelectedActivity.HasError)
                                {
                                    <div class="mb-3">
                                        <strong class="text-danger">Error:</strong>
                                        <div class="border p-2 bg-danger-subtle mt-1">
                                            @SelectedActivity.ErrorMessage
                                        </div>
                                        <small class="text-muted">Retry count: @SelectedActivity.RetryCount</small>
                                    </div>
                                }
                            </CardBody>
                        </Card>
                    </Column>
                </Row>
            }
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary" Clicked="CloseActivityDetailsModal">Close</Button>
        </ModalFooter>
    </ModalContent>
</Modal>

@code {
    [Parameter] public Guid CampaignId { get; set; }
    private DataGrid<CampaignActivityDto> activitiesDataGrid = default!;
    private AutoReplyCampaignDto? Campaign;
    private PagedResultDto<CampaignActivityDto>? CampaignActivities;
    private bool IsLoading = false;
    private int PageSize = 10;
    private int CurrentPage = 1;
    private string CurrentSorting = "";

    // Filters
    private bool? HasErrorFilter;
    private bool? PublicReplySentFilter;
    private bool? PrivateReplySentFilter;
    private string SearchText = "";

    // Activity Details Modal
    private Modal ActivityDetailsModal = default!;
    private CampaignActivityDto? SelectedActivity;

    protected override async Task OnInitializedAsync()
    {
        IsLoading = true;
        try
        {
            Campaign = await CampaignService.GetAsync(CampaignId);
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading campaign details: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadCampaignAndActivities()
    {
        try
        {
            Campaign = await CampaignService.GetAsync(CampaignId);
            await LoadActivities();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading campaign: {ex.Message}");
        }
    }

    private async Task LoadActivities()
    {
        try
        {
            var request = new GetActivitiesInput
                {
                    MaxResultCount = PageSize,
                    SkipCount = (CurrentPage - 1) * PageSize,
                    Sorting = CurrentSorting,
                    CampaignId = CampaignId,
                    HasError = HasErrorFilter,
                    PublicReplySent = PublicReplySentFilter,
                    PrivateReplySent = PrivateReplySentFilter,
                    SearchText = SearchText
                };

            CampaignActivities = await CampaignService.GetActivitiesAsync(request);
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading activities: {ex.Message}");
        }
    }

    private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<CampaignActivityDto> e)
    {
        IsLoading = true;
        await InvokeAsync(StateHasChanged);

        CurrentPage = e.Page;
        PageSize = e.PageSize;
        CurrentSorting = string.Join(",", e.Columns
            .Where(c => c.SortDirection != SortDirection.Default)
            .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : "")));

        await LoadActivities();

        IsLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task OnErrorFilterChanged(ChangeEventArgs e)
    {
        CurrentPage = 1;
        if (activitiesDataGrid is not null)
        {
            await activitiesDataGrid.Reload();
        }
    }

    private async Task OnPublicReplyFilterChanged(ChangeEventArgs e)
    {
        CurrentPage = 1;
        if (activitiesDataGrid is not null)
        {
            await activitiesDataGrid.Reload();
        }
    }

    private async Task OnPrivateReplyFilterChanged(ChangeEventArgs e)
    {
        CurrentPage = 1;
        if (activitiesDataGrid is not null)
        {
            await activitiesDataGrid.Reload();
        }
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchActivitiesAsync();
        }
    }

    private async Task SearchActivitiesAsync()
    {
        CurrentPage = 1;
        if (activitiesDataGrid is not null)
        {
            await activitiesDataGrid.Reload();
        }
    }

    private async Task ViewActivityDetailsAsync(CampaignActivityDto activity)
    {
        SelectedActivity = activity;
        await ActivityDetailsModal.Show();
    }

    private async Task CloseActivityDetailsModal()
    {
        await ActivityDetailsModal.Hide();
        SelectedActivity = null;
    }

    private void NavigateBack()
    {
        NavigationManager.NavigateTo("/campaigns");
    }
}
