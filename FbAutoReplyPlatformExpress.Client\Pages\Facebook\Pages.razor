@page "/facebook/pages"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Permissions
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.Application.Dtos
@using System.Collections.Generic
@using Microsoft.Extensions.Logging
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IFacebookPageService FacebookPageService
@inject IFacebookAuthService FacebookAuthService
@inject NavigationManager NavigationManager
@inject ILogger<Pages> _logger
@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Facebook.ViewPages)]

<PageTitle>Facebook Pages</PageTitle>

<Card>
    <CardHeader>
        <Row Class="justify-content-between">
            <Column ColumnSize="ColumnSize.IsAuto">
                <h2>
                    <Icon Name="IconName.FileAlt" />
                    Facebook Pages
                </h2>
            </Column>
            <Column ColumnSize="ColumnSize.IsAuto">
                <div class="d-flex gap-2">
                    @if (IsConnected)
                    {
                        <Button Color="Color.Info" Size="Size.Small" Outline="true" Clicked="@ValidateAllPageTokensAsync" Disabled="@IsValidatingTokens">
                            @if (IsValidatingTokens)
                            {
                                <div class="spinner-border spinner-border-sm me-1" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            }
                            else
                            {
                                <Icon Name="IconName.Shield" class="me-1" />
                            }
                            Validate Tokens
                        </Button>
                    }
                    @if (HasManagePermission)
                    {
                        <Button Color="Color.Primary" Clicked="ImportPagesAsync">
                            <Icon Name="IconName.Download" />
                            Import Pages
                        </Button>
                        <Button Color="Color.Secondary" Clicked="SyncAllPagesAsync" Class="ms-2">
                            <Icon Name="IconName.Sync" />
                            Sync All
                        </Button>
                    }
                </div>
            </Column>
        </Row>

        @if (IsConnected && PageTokenStatuses.Any())
        {
            <Row Class="mt-2">
                <Column ColumnSize="ColumnSize.Is12">
                    @{
                        var totalPages = PageTokenStatuses.Count;
                        var validPages = PageTokenStatuses.Values.Count(s => s.IsTokenValid && !s.RequiresReconnection);
                        var issuePages = PageTokenStatuses.Values.Count(s => s.RequiresReconnection);
                    }

                    @if (issuePages > 0)
                    {
                        <Alert Color="Color.Warning" Visible="true">
                            <AlertMessage>
                                <Icon Name="IconName.ExclamationTriangle" class="me-2" />
                                <strong>Token Issues Detected</strong>
                            </AlertMessage>
                            <AlertDescription>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        @issuePages of @totalPages pages have token issues that require attention.
                                        <a href="/facebook/connection" class="alert-link">Reconnect your Facebook account</a> to resolve these issues.
                                    </div>
                                    <Button Color="Color.Primary" Size="Size.Small" Clicked="@(() => NavigationManager.NavigateTo("/facebook/connection"))">
                                        <Icon Name="IconName.Link" class="me-1" />
                                        Reconnect Facebook
                                    </Button>
                                </div>
                            </AlertDescription>
                        </Alert>
                    }
                    else if (validPages == totalPages && totalPages > 0)
                    {
                        <Alert Color="Color.Success" Visible="true">
                            <AlertMessage>
                                <Icon Name="IconName.CheckCircle" class="me-2" />
                                All @totalPages page tokens are valid and active.
                            </AlertMessage>
                        </Alert>
                    }
                </Column>
            </Row>
        }
    </CardHeader>
    <CardBody>
        @if (ShowTokenErrorAlert)
        {
            <FacebookErrorHandler ErrorType="@CurrentErrorType"
                                  ErrorMessage="@CurrentErrorMessage"
                                  AffectedPages="@AffectedPageNames"
                                  ShowRetryButton="true"
                                  ShowReconnectButton="true"
                                  ShowTechnicalDetails="false"
                                  AutoRedirectOnCritical="true"
                                  OnRetryClicked="@ValidateAllPageTokensAsync"
                                  OnDismissClicked="@(() => ShowTokenErrorAlert = false)" />
        }
        @if (!IsConnected)
        {
            <FacebookErrorHandler ErrorType="FacebookErrorType.ConnectionLost"
                                  ErrorMessage="You need to connect your Facebook account first to manage your pages."
                                  ShowRetryButton="false"
                                  ShowReconnectButton="true"
                                  AutoRedirectOnCritical="false" />
        }
        else if (FacebookPages == null)  // Initial state before first load
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading Facebook Pages...</p>
            </div>
        }
        else if (IsLoading)
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Refreshing  Facebook Pages...</p>
            </div>
        }
        else if (FacebookPages.Items.Any())
        {
            <DataGrid TItem="FacebookPageDto"
                      Data="FacebookPages.Items"
                      ReadData="OnDataGridReadAsync"
                      TotalItems="(int)FacebookPages.TotalCount"
                      ShowPager="true"
                      PageSize="PageSize"
                      Responsive="true">
                <DataGridColumns>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.PageProfilePictureUrl)" Caption="" Sortable="false" Width="80px">
                        <DisplayTemplate>
                            @if (!string.IsNullOrEmpty(context.PageProfilePictureUrl))
                            {
                                <img src="@context.PageProfilePictureUrl" alt="Page Picture" style="width: 40px; height: 40px; border-radius: 50%;" />
                            }
                            else
                            {
                                <Icon Name="IconName.FileAlt" Style="font-size: 24px;" />
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.PageName)" Caption="Page Name" Sortable="true">
                        <DisplayTemplate>
                            <strong>@context.PageName</strong>
                            <br />
                            <small class="text-muted">@context.Category</small>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.FollowersCount)" Caption="Followers" Sortable="true">
                        <DisplayTemplate>
                            @context.FollowersCount.ToString("N0")
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.IsConnected)" Caption="Status" Sortable="true">
                        <DisplayTemplate>
                            <div class="d-flex flex-column gap-1">
                                <div>
                                    @if (context.IsConnected)
                                    {
                                        <Badge Color="Color.Success">Connected</Badge>
                                    }
                                    else
                                    {
                                        <Badge Color="Color.Secondary">Disconnected</Badge>
                                    }
                                    @if (context.WebhookSubscribed)
                                    {
                                        <Badge Color="Color.Info" Class="ms-1">Webhook Active</Badge>
                                    }
                                </div>
                                @if (context.IsConnected)
                                {
                                    <div>
                                        <FacebookPageTokenStatus PageId="@context.Id"
                                                                  TokenStatus="@GetPageTokenStatus(context.Id)"
                                                                  ShowErrorDetails="false"
                                                                  ShowScopeDetails="false"
                                                                  OnReconnectClicked="@(() => HandlePageReconnectAsync(context.Id))"
                                                                  OnStatusChanged="@((status) => HandleTokenStatusChanged(context.Id, status))" />
                                    </div>
                                }
                            </div>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Field="@nameof(FacebookPageDto.LastSyncAt)" Caption="Last Sync" Sortable="true">
                        <DisplayTemplate>
                            @if (context.LastSyncAt.HasValue)
                            {
                                @context.LastSyncAt.Value.ToString("MMM dd, yyyy HH:mm")
                            }
                            else
                            {
                                <span class="text-muted">Never</span>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="FacebookPageDto" Caption="Actions" Sortable="false" Width="200px">
                        <DisplayTemplate>
                            @if (HasManagePermission)
                            {
                                <Dropdown>
                                    <DropdownToggle Color="Color.Primary" Size="Size.Small">
                                        Actions
                                    </DropdownToggle>
                                    <DropdownMenu>
                                        <DropdownItem Clicked="() => SyncPageAsync(context.Id)">
                                            <Icon Name="IconName.Sync" />
                                            Sync
                                        </DropdownItem>
                                        @if (context.IsConnected && !context.WebhookSubscribed)
                                        {
                                            <DropdownItem Clicked="() => SubscribeWebhookAsync(context.Id)">
                                                <Icon Name="IconName.Bell" />
                                                Enable Webhook
                                            </DropdownItem>
                                        }
                                        @if (context.WebhookSubscribed)
                                        {
                                            <DropdownItem Clicked="() => UnsubscribeWebhookAsync(context.Id)">
                                                <Icon Name="IconName.BellSlash" />
                                                Disable Webhook
                                            </DropdownItem>
                                        }
                                        <DropdownDivider />
                                        @if (context.IsConnected)
                                        {
                                            <DropdownItem Clicked="() => DisconnectPageAsync(context.Id)">
                                                <Icon Name="IconName.Times" />
                                                Disconnect
                                            </DropdownItem>
                                        }
                                        else
                                        {
                                            <DropdownItem Clicked="() => ReconnectPageAsync(context.Id)">
                                                <Icon Name="IconName.Link" />
                                                Reconnect
                                            </DropdownItem>
                                        }
                                        <DropdownDivider />
                                        <DropdownItem Clicked="() => ViewPostsAsync(context.Id)" Class="text-primary">
                                            <Icon Name="IconName.Comments" />
                                            View Posts
                                        </DropdownItem>
                                    </DropdownMenu>
                                </Dropdown>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                </DataGridColumns>
            </DataGrid>
        }
        else
        {
            <Alert Color="Color.Info" Visible="true">
                <Icon Name="IconName.Info" />
                No Facebook Pages found. Click "Import Pages" to import your Facebook Pages.
            </Alert>
        }
    </CardBody>
</Card>

<!-- Import Pages Modal -->
<Modal @ref="ImportPagesModal">
    <ModalContent Centered="true" Size="ModalSize.Large">
        <ModalHeader>
            <ModalTitle>Import Facebook Pages</ModalTitle>
            <CloseButton />
        </ModalHeader>
        <ModalBody>
            @if (AvailablePages?.Any() == true)
            {
                <p>Select the pages you want to import:</p>
                @foreach (var pageItem in AvailablePages)
                {
                    <Card Class="mb-2">
                        <CardBody>
                            <Row>
                                <Column ColumnSize="ColumnSize.IsAuto">
                                    <Check TValue="bool" @bind-Checked="@pageItem.IsSelected" />
                                </Column>
                                <Column ColumnSize="ColumnSize.IsAuto">
                                    @if (!string.IsNullOrEmpty(pageItem.PageProfilePictureUrl))
                                    {
                                        <img src="@pageItem.PageProfilePictureUrl" alt="Page Picture" style="width: 40px; height: 40px; border-radius: 50%;" />
                                    }
                                </Column>
                                <Column>
                                    <strong>@pageItem.PageName</strong>
                                    <br />
                                    <small class="text-muted">@pageItem.Category • @pageItem.FollowersCount.ToString("N0") followers</small>
                                </Column>
                            </Row>
                        </CardBody>
                    </Card>
                }
            }
            else if (IsLoadingAvailablePages)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading available pages...</p>
                </div>
            }
            else
            {
                <Alert Color="Color.Warning" Visible="true">
                    No pages available for import.
                </Alert>
            }
        </ModalBody>
        <ModalFooter>
            <Button Color="Color.Secondary" Clicked="CloseImportModal">Cancel</Button>
            <Button Color="Color.Primary" Clicked="ImportSelectedPagesAsync" Disabled="@(!AvailablePages?.Any(p => p.IsSelected) == true)">
                Import Selected Pages
            </Button>
        </ModalFooter>
    </ModalContent>
</Modal>

@code {
    private PagedResultDto<FacebookPageDto>? FacebookPages;
    private bool IsLoading = false;
    private bool IsConnected = false;
    private bool HasManagePermission = false;
    private int PageSize = 10;
    private int CurrentPage = 1;
    private string CurrentSorting = "";

    // Import Pages Modal
    private Modal ImportPagesModal = default!;
    private List<SelectableFacebookPageDto>? AvailablePages;
    private bool IsLoadingAvailablePages = false;

    // Token validation
    private Dictionary<Guid, FacebookPageTokenStatusDto> PageTokenStatuses = new();
    private bool IsValidatingTokens = false;

    // Error handling
    private bool ShowTokenErrorAlert = false;
    private FacebookErrorType CurrentErrorType = FacebookErrorType.General;
    private string CurrentErrorMessage = string.Empty;
    private List<string> AffectedPageNames = new();

    protected override async Task OnInitializedAsync()
    {
        HasManagePermission = await AuthorizationService.IsGrantedAsync(FbAutoReplyPlatformExpressPermissions.Facebook.ManagePages);
        await CheckConnectionAsync();

        // Load initial data if connected
        if (IsConnected)
        {
            await LoadPages();
            await ValidateAllPageTokensAsync();
        }
        else
        {
            // Initialize with empty result to show the "not connected" state properly
            FacebookPages = new PagedResultDto<FacebookPageDto>(0, new List<FacebookPageDto>());
        }
    }

    private async Task CheckConnectionAsync()
    {
        try
        {
            var wasConnected = IsConnected;
            IsConnected = await FacebookAuthService.IsConnectedToFacebookAsync();

            // If connection status changed from false to true, load the pages
            if (!wasConnected && IsConnected)
            {
                await LoadPages();
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error checking Facebook connection: {ex.Message}");
        }
    }

    private async Task LoadPages()
    {
        if (!IsConnected)
        {
            FacebookPages = null;
            return;
        }

        try
        {
            IsLoading = true;
            var request = new PagedAndSortedResultRequestDto
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };

            FacebookPages = await FacebookPageService.GetListAsync(request);
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading pages: {ex.Message}");

            FacebookPages = new PagedResultDto<FacebookPageDto>(0, new List<FacebookPageDto>());
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<FacebookPageDto> e)
    {
        if (!IsConnected) return;

        // Prevent infinite loop - only reload if pagination/sorting actually changed
        var newPage = e.Page;
        var newPageSize = e.PageSize;
        var newSorting = string.Join(",", e.Columns
            .Where(c => c.SortDirection != SortDirection.Default)
            .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : "")));

        // Only reload if something actually changed
        if (newPage != CurrentPage || newPageSize != PageSize || newSorting != CurrentSorting)
        {
            CurrentPage = newPage;
            PageSize = newPageSize;
            CurrentSorting = newSorting;

            await LoadPages();
        }
    }

    private void NavigateToConnection()
    {
        var returnUrl = Uri.EscapeDataString("/facebook/pages");
        NavigationManager.NavigateTo($"/facebook/connection?from=pages&returnUrl={returnUrl}");
    }

    private async Task ImportPagesAsync()
    {
        try
        {
            IsLoadingAvailablePages = true;
            await ImportPagesModal.Show();

            var availablePages = await FacebookPageService.GetAvailablePagesFromFacebookAsync();
            AvailablePages = availablePages.Select(p => new SelectableFacebookPageDto(p)).ToList();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading available pages: {ex.Message}");
        }
        finally
        {
            IsLoadingAvailablePages = false;
        }
    }

    private async Task CloseImportModal()
    {
        await ImportPagesModal.Hide();
        AvailablePages = null;
    }

    private async Task ImportSelectedPagesAsync()
    {
        try
        {
            var selectedPages = AvailablePages?.Where(p => p.IsSelected).Select(p => p.ToImportDto()).ToList();
            if (selectedPages?.Any() == true)
            {
                await FacebookPageService.ImportMultiplePagesAsync(selectedPages);
                await Message.Success($"Successfully imported {selectedPages.Count} page(s)!");
                await CloseImportModal();
                await LoadPages();
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error importing pages: {ex.Message}");
        }
    }

    private async Task SyncPageAsync(Guid pageId)
    {
        try
        {
            await FacebookPageService.SyncPageInfoAsync(pageId);
            await Message.Success("Page synced successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error syncing page: {ex.Message}");
        }
    }

    private async Task SyncAllPagesAsync()
    {
        try
        {
            await FacebookPageService.SyncAllPagesAsync();
            await Message.Success("All pages synced successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error syncing pages: {ex.Message}");
        }
    }

    private async Task SubscribeWebhookAsync(Guid pageId)
    {
        try
        {
            await FacebookPageService.SubscribeToWebhookAsync(pageId);
            await Message.Success("Webhook enabled successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error enabling webhook: {ex.Message}");
        }
    }

    private async Task UnsubscribeWebhookAsync(Guid pageId)
    {
        try
        {
            await FacebookPageService.UnsubscribeFromWebhookAsync(pageId);
            await Message.Success("Webhook disabled successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error disabling webhook: {ex.Message}");
        }
    }

    private async Task DisconnectPageAsync(Guid pageId)
    {
        var confirmed = await Message.Confirm("Are you sure you want to disconnect this page?");
        if (confirmed)
        {
            try
            {
                await FacebookPageService.DisconnectPageAsync(pageId);
                await Message.Success("Page disconnected successfully!");
                await LoadPages();
            }
            catch (Exception ex)
            {
                await Message.Error($"Error disconnecting page: {ex.Message}");
            }
        }
    }

    private async Task ReconnectPageAsync(Guid pageId)
    {
        try
        {
            await FacebookPageService.ReconnectPageAsync(pageId);
            await Message.Success("Page reconnected successfully!");
            await LoadPages();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error reconnecting page: {ex.Message}");
        }
    }

    private void ViewPostsAsync(Guid pageId)
    {
        NavigationManager.NavigateTo($"/facebook/posts?pageId={pageId}");
    }

    private async Task ValidateAllPageTokensAsync()
    {
        if (!IsConnected || FacebookPages?.Items == null || !FacebookPages.Items.Any())
        {
            return;
        }

        try
        {
            IsValidatingTokens = true;
            ShowTokenErrorAlert = false; // Hide previous errors
            StateHasChanged();

            var tokenStatuses = await FacebookAuthService.ValidateAllPageTokensAsync();

            PageTokenStatuses.Clear();
            foreach (var status in tokenStatuses)
            {
                PageTokenStatuses[status.PageId] = status;
            }

            // Analyze token issues and show appropriate error handler
            var pagesWithIssues = tokenStatuses.Where(s => s.RequiresReconnection).ToList();
            if (pagesWithIssues.Any())
            {
                var expiredPages = pagesWithIssues.Where(p => p.IsTokenExpired).ToList();
                var invalidPages = pagesWithIssues.Where(p => !p.IsTokenValid && !p.IsTokenExpired).ToList();
                var permissionPages = pagesWithIssues.Where(p => p.MissingScopes.Any()).ToList();

                AffectedPageNames = pagesWithIssues.Select(p => p.PageName).ToList();

                if (expiredPages.Any())
                {
                    CurrentErrorType = FacebookErrorType.TokenExpired;
                    CurrentErrorMessage = $"Facebook access tokens have expired for {expiredPages.Count} page(s). Re-authentication is required to restore access.";
                }
                else if (invalidPages.Any())
                {
                    CurrentErrorType = FacebookErrorType.TokenInvalid;
                    CurrentErrorMessage = $"Facebook access tokens are invalid for {invalidPages.Count} page(s). Please reconnect your Facebook account.";
                }
                else if (permissionPages.Any())
                {
                    CurrentErrorType = FacebookErrorType.MissingPermissions;
                    CurrentErrorMessage = $"Missing required permissions for {permissionPages.Count} page(s). Please reconnect to grant necessary permissions.";
                }
                else
                {
                    CurrentErrorType = FacebookErrorType.General;
                    CurrentErrorMessage = $"Token issues detected for {pagesWithIssues.Count} page(s). Please reconnect your Facebook account to restore access.";
                }

                ShowTokenErrorAlert = true;
            }
            else
            {
                ShowTokenErrorAlert = false;
            }
        }
        catch (Exception ex)
        {
            CurrentErrorType = FacebookErrorType.General;
            CurrentErrorMessage = "Failed to validate page tokens. Please try again or reconnect your Facebook account.";
            AffectedPageNames.Clear();
            ShowTokenErrorAlert = true;

            _logger.LogError(ex, "Error validating page tokens");
        }
        finally
        {
            IsValidatingTokens = false;
            StateHasChanged();
        }
    }

    private FacebookPageTokenStatusDto? GetPageTokenStatus(Guid pageId)
    {
        return PageTokenStatuses.TryGetValue(pageId, out var status) ? status : null;
    }

    private async Task HandleTokenStatusChanged(Guid pageId, FacebookPageTokenStatusDto status)
    {
        PageTokenStatuses[pageId] = status;
        StateHasChanged();
    }

    private async Task HandlePageReconnectAsync(Guid pageId)
    {
        var confirmed = await Message.Confirm(
            "To fix page token issues, you need to reconnect your Facebook account. This will refresh tokens for all your pages. Continue?",
            "Reconnect Facebook Account");

        if (confirmed)
        {
            var currentUrl = NavigationManager.Uri;
            var returnUrl = Uri.EscapeDataString("/facebook/pages");
            NavigationManager.NavigateTo($"/facebook/connection?from=pages&returnUrl={returnUrl}");
        }
    }

    public class SelectableFacebookPageDto
    {
        public FacebookPageImportDto ImportDto { get; set; }
        public bool IsSelected { get; set; }

        public SelectableFacebookPageDto(FacebookPageImportDto importDto)
        {
            ImportDto = importDto;
            IsSelected = false;
        }

        public string FacebookPageId => ImportDto.FacebookPageId;
        public string PageName => ImportDto.PageName;
        public string? PageProfilePictureUrl => ImportDto.PageProfilePictureUrl;
        public string? Category => ImportDto.Category;
        public int FollowersCount => ImportDto.FollowersCount;

        public FacebookPageImportDto ToImportDto() => ImportDto;
    }
}
